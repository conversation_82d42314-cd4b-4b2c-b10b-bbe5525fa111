/**
 * Web端设置更新测试脚本
 * 用于验证Web端设置数据更新后是否正确同步到蓝牙发送数据
 */

const testWebSettingsUpdate = async () => {
  console.log('🔧 开始测试Web端设置更新同步...');
  
  try {
    // 1. 检查当前平台
    console.log('📊 步骤1: 检查当前平台...');
    const isAndroid = window.Ionic?.getPlatform?.()?.includes('android') || false;
    const isIOS = window.Ionic?.getPlatform?.()?.includes('ios') || false;
    const isWeb = !isAndroid && !isIOS;
    
    console.log(`📱 当前平台: ${isAndroid ? 'Android' : isIOS ? 'iOS' : 'Web'}`);
    
    if (isAndroid) {
      console.log('⚠️ 当前是Android平台，此测试主要针对Web端，但仍会执行基础测试');
    }
    
    // 2. 检查事件监听器是否正常工作
    console.log('📊 步骤2: 测试事件监听器...');

    let eventReceived = false;
    const testEventListener = (event) => {
      console.log('✅ 收到bluetoothDataUpdated事件:', event.detail);
      eventReceived = true;
    };

    window.addEventListener('bluetoothDataUpdated', testEventListener);

    // 触发测试事件
    window.dispatchEvent(new CustomEvent('bluetoothDataUpdated', {
      detail: {
        writeData: [0x0F, 0x04, 0xF5, 0x58, 0x2E, 0xB2, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E],
        timestamp: Date.now(),
        source: 'webSettingsUpdateTest',
        forceUpdate: true
      }
    }));
    
    // 等待事件处理
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (eventReceived) {
      console.log('✅ 事件监听器工作正常');
    } else {
      console.warn('⚠️ 事件监听器未收到事件');
    }
    
    window.removeEventListener('bluetoothDataUpdated', testEventListener);
    
    // 3. 测试useMessage Hook的数据缓存更新
    console.log('📊 步骤3: 测试useMessage数据缓存更新...');
    
    try {
      // 检查useMessage是否可用
      if (window.useMessage) {
        const messageHook = window.useMessage();
        if (messageHook.updateSendDataCache) {
          console.log('🔧 调用updateSendDataCache...');
          await messageHook.updateSendDataCache();
          console.log('✅ useMessage数据缓存更新成功');
        } else {
          console.warn('⚠️ useMessage.updateSendDataCache方法不可用');
        }
      } else {
        console.warn('⚠️ useMessage Hook不可用，可能需要在Vue组件中测试');
      }
    } catch (error) {
      console.warn('⚠️ useMessage测试失败:', error.message);
    }
    
    // 4. 测试智能蓝牙Hook的数据更新
    console.log('📊 步骤4: 测试智能蓝牙Hook数据更新...');
    
    try {
      if (window.useSmartBluetoothMessage) {
        const smartHook = window.useSmartBluetoothMessage();
        if (smartHook.updateSmartBluetoothData) {
          console.log('🔧 调用updateSmartBluetoothData...');
          await smartHook.updateSmartBluetoothData();
          console.log('✅ 智能蓝牙Hook数据更新成功');
        } else {
          console.warn('⚠️ updateSmartBluetoothData方法不可用');
        }
      } else {
        console.warn('⚠️ useSmartBluetoothMessage Hook不可用，可能需要在Vue组件中测试');
      }
    } catch (error) {
      console.warn('⚠️ 智能蓝牙Hook测试失败:', error.message);
    }
    
    // 5. 测试蓝牙数据管理器
    console.log('📊 步骤5: 测试蓝牙数据管理器...');
    
    try {
      if (window.bluetoothDataManager) {
        console.log('🔧 调用bluetoothDataManager.updateSettingsAndSend...');
        const success = await window.bluetoothDataManager.updateSettingsAndSend();
        if (success) {
          console.log('✅ 蓝牙数据管理器更新成功');
        } else {
          console.warn('⚠️ 蓝牙数据管理器更新失败');
        }
      } else {
        console.warn('⚠️ bluetoothDataManager不可用');
      }
    } catch (error) {
      console.warn('⚠️ 蓝牙数据管理器测试失败:', error.message);
    }
    
    // 6. 模拟设置页面保存操作
    console.log('📊 步骤6: 模拟设置页面保存操作...');
    
    try {
      // 模拟设置数据更新
      const mockSettingsData = {
        maxSpeed: 25,
        dimension: 26,
        p1: 1,
        p2: 1,
        p3: 1,
        p4: 0,
        p5: 36
      };
      
      console.log('🔧 模拟设置数据:', mockSettingsData);
      
      // 触发蓝牙数据更新事件（模拟设置页面的行为）
      window.dispatchEvent(new CustomEvent('bluetoothDataUpdated', {
        detail: {
          settings: mockSettingsData,
          timestamp: Date.now(),
          source: 'webSettingsUpdateTest.mockSave',
          forceUpdate: true
        }
      }));
      
      console.log('✅ 模拟设置保存操作完成');
      
    } catch (error) {
      console.error('❌ 模拟设置保存操作失败:', error);
    }
    
    // 7. 生成测试报告
    console.log('📊 步骤7: 生成测试报告...');
    
    const testReport = {
      platform: isAndroid ? 'Android' : isIOS ? 'iOS' : 'Web',
      eventListenerWorking: eventReceived,
      useMessageAvailable: !!window.useMessage,
      smartBluetoothAvailable: !!window.useSmartBluetoothMessage,
      dataManagerAvailable: !!window.bluetoothDataManager,
      timestamp: new Date().toISOString()
    };
    
    console.log('📋 测试报告:', testReport);
    
    // 8. 提供修复建议
    console.log('📊 步骤8: 修复建议...');
    
    const suggestions = [];
    
    if (!eventReceived) {
      suggestions.push('🔧 事件监听器可能未正确设置，检查useMessage.ts中的事件监听器');
    }
    
    if (!window.useMessage && !isAndroid) {
      suggestions.push('🔧 Web端useMessage Hook不可用，可能需要在Vue组件中初始化');
    }
    
    if (!window.bluetoothDataManager) {
      suggestions.push('🔧 蓝牙数据管理器不可用，检查是否正确导入');
    }
    
    if (suggestions.length > 0) {
      console.log('💡 修复建议:');
      suggestions.forEach(suggestion => console.log(suggestion));
    } else {
      console.log('✅ 所有测试项目都正常工作！');
    }
    
    console.log('🎉 Web端设置更新测试完成！');
    
    return testReport;
    
  } catch (error) {
    console.error('❌ Web端设置更新测试失败:', error);
    throw error;
  }
};

// 提供全局访问
window.testWebSettingsUpdate = testWebSettingsUpdate;

// 提供便捷的测试工具
window.webSettingsTestUtils = {
  // 触发蓝牙数据更新事件
  triggerBluetoothDataUpdate: (forceUpdate = true) => {
    window.dispatchEvent(new CustomEvent('bluetoothDataUpdated', {
      detail: {
        writeData: [0x0F, 0x04, 0xF5, 0x58, 0x2E, 0xB2, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E],
        timestamp: Date.now(),
        source: 'webSettingsTestUtils',
        forceUpdate
      }
    }));
    console.log('✅ 已触发蓝牙数据更新事件, forceUpdate:', forceUpdate);
  },
  
  // 检查事件监听器
  checkEventListeners: () => {
    const events = ['bluetoothDataUpdated', 'navigationDataUpdated'];
    events.forEach(eventName => {
      const hasListeners = window.getEventListeners?.(window)?.[eventName]?.length > 0;
      console.log(`${hasListeners ? '✅' : '❌'} ${eventName}: ${hasListeners ? '有监听器' : '无监听器'}`);
    });
  }
};

console.log('🎯 Web端设置更新测试脚本已加载！');
console.log('💡 使用 testWebSettingsUpdate() 开始测试');
console.log('🔧 使用 webSettingsTestUtils 进行调试');
