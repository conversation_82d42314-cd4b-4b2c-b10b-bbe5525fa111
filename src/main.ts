import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './store'
import { IonicVue } from '@ionic/vue'

/* Core CSS required for Ionic components to work properly */
import '@ionic/vue/css/core.css'

/* Basic CSS for apps built with Ionic */
import '@ionic/vue/css/normalize.css'
import '@ionic/vue/css/structure.css'
import '@ionic/vue/css/typography.css'

/* Optional CSS utils that can be commented out */
import '@ionic/vue/css/padding.css'
import '@ionic/vue/css/float-elements.css'
import '@ionic/vue/css/text-alignment.css'
import '@ionic/vue/css/text-transformation.css'
import '@ionic/vue/css/flex-utils.css'
import '@ionic/vue/css/display.css'

/* Theme variables */
import './theme/variables.css'
import 'mapbox-gl/dist/mapbox-gl.css'
import { ScreenOrientation } from '@capacitor/screen-orientation'
import { Capacitor } from '@capacitor/core'
import bluetoothAutoSenderPlugin from './plugins/bluetoothAutoSender'

// 只在开发环境中加载调试工具
if (import.meta.env.DEV) {
  import('eruda').then(eruda => {
    eruda.default.init()
  }).catch(err => {
    console.warn('Failed to load eruda:', err)
  })
}

if (Capacitor.isNativePlatform()) {
  ScreenOrientation.lock({ orientation: 'portrait-primary' })
}
const app = createApp(App)
  .use(pinia)
  .use(IonicVue)
  .use(router)
  .use(bluetoothAutoSenderPlugin) // 🔧 启用全局蓝牙自动发送

router.isReady().then(async () => {
  app.mount('#app')
})
