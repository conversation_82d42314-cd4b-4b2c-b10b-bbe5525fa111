import { watch, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useBleStore } from '@/store/useBleStore';
import { useSmartBluetoothMessage } from './useSmartBluetoothMessage';

/**
 * 蓝牙自动发送Hook
 * 全局监听蓝牙连接状态，自动启动/停止数据发送
 * 
 * 功能：
 * 1. 监听蓝牙连接状态变化
 * 2. 连接成功时自动启动智能发送
 * 3. 断开连接时自动停止发送
 * 4. 根据平台自动选择最佳发送方案
 */
export function useBluetoothAutoSender() {
  const { connectedDevice } = storeToRefs(useBleStore());
  const smartBluetoothHook = useSmartBluetoothMessage();
  
  const {
    sendMessage,
    stopSendMessage,
    isServiceRunning,
    currentMethod,
    updateSmartBluetoothData
  } = smartBluetoothHook;

  // 监听器状态
  const isListenerActive = ref(false);
  const lastConnectionState = ref(false);

  /**
   * 启动蓝牙连接监听器
   */
  const startBluetoothConnectionListener = () => {
    if (isListenerActive.value) {
      console.log('🔄 蓝牙连接监听器已经在运行');
      return;
    }

    console.log('🎯 启动全局蓝牙连接监听器');
    
    // 初始化状态
    lastConnectionState.value = connectedDevice.value?.isPaired || false;
    isListenerActive.value = true;

    // 监听蓝牙连接状态变化
    const stopWatcher = watch(
      () => connectedDevice.value?.isPaired || false,
      async (isPaired, wasPaired) => {
        console.log(`🔄 蓝牙连接状态变化: ${wasPaired} -> ${isPaired}`);
        
        if (isPaired && !wasPaired) {
          // 蓝牙刚刚连接成功
          await handleBluetoothConnected();
        } else if (!isPaired && wasPaired) {
          // 蓝牙断开连接
          await handleBluetoothDisconnected();
        }
        
        lastConnectionState.value = isPaired;
      },
      { immediate: false } // 不立即执行，只监听变化
    );

    // 返回停止监听的方法
    return stopWatcher;
  };

  /**
   * 处理蓝牙连接成功
   */
  const handleBluetoothConnected = async () => {
    console.log("🎉 蓝牙连接成功，准备启动智能发送");
    console.log(`📱 使用 ${currentMethod.value} 发送方案`);
    
    // 延迟确保连接稳定
    setTimeout(async () => {
      try {
        // 再次确认连接状态（防止快速连接/断开）
        if (!connectedDevice.value?.isPaired) {
          console.warn("⚠️ 连接状态已变化，取消自动启动");
          return;
        }

        // 检查是否已经在发送
        if (isServiceRunning.value) {
          console.log("✅ 发送服务已在运行，更新数据确保最新");
          if (updateSmartBluetoothData) {
            await updateSmartBluetoothData();
          }
          return;
        }

        console.log("🚀 启动智能蓝牙发送服务");
        await sendMessage();
        console.log("✅ 智能蓝牙发送启动成功");
        
      } catch (error) {
        console.error("❌ 蓝牙连接后启动发送失败:", error);
      }
    }, 1500); // 1.5秒延迟确保连接稳定
  };

  /**
   * 处理蓝牙断开连接
   */
  const handleBluetoothDisconnected = async () => {
    console.log("📱 蓝牙连接断开，停止发送服务");
    
    try {
      if (isServiceRunning.value) {
        await stopSendMessage();
        console.log("✅ 蓝牙发送服务已停止");
      }
    } catch (error) {
      console.error("❌ 停止蓝牙发送失败:", error);
    }
  };

  /**
   * 手动检查并恢复发送状态
   * 用于页面进入时确保服务正常运行
   */
  const checkAndRestoreBluetoothSending = async () => {
    if (!connectedDevice.value?.isPaired) {
      console.log("🔍 蓝牙未连接，无需检查发送状态");
      return;
    }

    console.log("🔍 检查蓝牙发送状态");
    console.log(`📱 当前使用: ${currentMethod.value} 发送方案`);

    try {
      const isCurrentlySending = isServiceRunning.value;
      console.log("🔍 当前蓝牙发送状态:", isCurrentlySending);

      if (!isCurrentlySending) {
        console.log("🚀 检测到发送服务未运行，重新启动");
        await sendMessage();
        console.log("✅ 智能蓝牙发送重启成功");
      } else {
        console.log("✅ 发送服务正常运行，更新数据确保最新");
        if (updateSmartBluetoothData) {
          await updateSmartBluetoothData();
        }
      }
    } catch (error) {
      console.error("❌ 检查蓝牙发送状态失败:", error);
    }
  };

  /**
   * 🔧 新增：强制更新蓝牙发送数据
   * 用于设置更新后立即同步数据
   */
  const forceUpdateBluetoothData = async () => {
    console.log("🔧 强制更新蓝牙发送数据");

    try {
      if (updateSmartBluetoothData) {
        await updateSmartBluetoothData();
        console.log("✅ 蓝牙发送数据强制更新完成");
      } else {
        console.warn("⚠️ updateSmartBluetoothData 方法不可用");
      }
    } catch (error) {
      console.error("❌ 强制更新蓝牙发送数据失败:", error);
    }
  };

  /**
   * 停止蓝牙连接监听器
   */
  const stopBluetoothConnectionListener = () => {
    if (isListenerActive.value) {
      isListenerActive.value = false;
      console.log('🛑 蓝牙连接监听器已停止');
    }
  };

  /**
   * 获取当前状态信息
   */
  const getBluetoothAutoSenderStatus = () => {
    return {
      isListenerActive: isListenerActive.value,
      isBluetoothConnected: connectedDevice.value?.isPaired || false,
      isServiceRunning: isServiceRunning.value,
      currentMethod: currentMethod.value,
      lastConnectionState: lastConnectionState.value
    };
  };

  return {
    // 主要控制方法
    startBluetoothConnectionListener,
    stopBluetoothConnectionListener,
    checkAndRestoreBluetoothSending,
    forceUpdateBluetoothData, // 🔧 新增：强制更新方法

    // 状态信息
    isListenerActive,
    getBluetoothAutoSenderStatus,

    // 便捷访问智能蓝牙Hook的方法
    isServiceRunning,
    currentMethod,
    
    // 调试信息
    debugInfo: () => ({
      hookName: 'useBluetoothAutoSender',
      description: '全局蓝牙自动发送管理器',
      features: [
        '监听蓝牙连接状态变化',
        '自动启动/停止数据发送',
        '智能平台方案选择',
        '连接状态恢复检查'
      ]
    })
  };
}
